import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/theme/app_theme.dart';
import '../../services/auth_service.dart';

class AccountSettingsScreen extends StatefulWidget {
  const AccountSettingsScreen({super.key});

  @override
  State<AccountSettingsScreen> createState() => _AccountSettingsScreenState();
}

class _AccountSettingsScreenState extends State<AccountSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _displayNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _currentPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  
  bool _isLoading = false;
  bool _showPasswordFields = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _emailController.dispose();
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _loadUserData() {
    final authService = Provider.of<AuthService>(context, listen: false);
    final user = authService.currentUser;
    
    if (user != null) {
      _displayNameController.text = user.displayName;
      _emailController.text = user.email;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.spotifyBlack,
      appBar: AppBar(
        backgroundColor: AppTheme.spotifyBlack,
        title: const Text(
          'Account Settings',
          style: TextStyle(color: AppTheme.spotifyWhite),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.spotifyWhite),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveChanges,
            child: Text(
              'Save',
              style: TextStyle(
                color: _isLoading ? AppTheme.spotifyOffWhite : AppTheme.spotifyGreen,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Picture Section
              _buildProfilePictureSection(),
              
              const SizedBox(height: 32),
              
              // Basic Information
              _buildSectionHeader('Basic Information'),
              _buildBasicInfoSection(),
              
              const SizedBox(height: 32),
              
              // Password Section
              _buildSectionHeader('Password'),
              _buildPasswordSection(),
              
              const SizedBox(height: 32),
              
              // Connected Accounts
              _buildSectionHeader('Connected Accounts'),
              _buildConnectedAccountsSection(),
              
              const SizedBox(height: 32),
              
              // Danger Zone
              _buildSectionHeader('Danger Zone'),
              _buildDangerZoneSection(),
              
              const SizedBox(height: 100), // Bottom padding
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: AppTheme.darkTheme.textTheme.titleLarge?.copyWith(
          color: AppTheme.spotifyWhite,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildProfilePictureSection() {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final user = authService.currentUser;
        
        return Center(
          child: Column(
            children: [
              Stack(
                children: [
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppTheme.spotifyGrey,
                      border: Border.all(color: AppTheme.spotifyLightGrey),
                    ),
                    child: user?.profileImageUrl != null
                        ? ClipOval(
                            child: Image.network(
                              user!.profileImageUrl!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) => _buildAvatarPlaceholder(),
                            ),
                          )
                        : _buildAvatarPlaceholder(),
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 36,
                      height: 36,
                      decoration: const BoxDecoration(
                        color: AppTheme.spotifyGreen,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        icon: const Icon(
                          Icons.camera_alt,
                          color: AppTheme.spotifyBlack,
                          size: 18,
                        ),
                        onPressed: _changeProfilePicture,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: _changeProfilePicture,
                child: const Text(
                  'Change Profile Picture',
                  style: TextStyle(
                    color: AppTheme.spotifyGreen,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAvatarPlaceholder() {
    return const Center(
      child: Icon(
        Icons.person,
        size: 60,
        color: AppTheme.spotifyOffWhite,
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      children: [
        // Display Name
        TextFormField(
          controller: _displayNameController,
          style: const TextStyle(color: AppTheme.spotifyWhite),
          decoration: InputDecoration(
            labelText: 'Display Name',
            labelStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyGreen, width: 2),
            ),
            filled: true,
            fillColor: AppTheme.spotifyGrey,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Display name is required';
            }
            if (value.trim().length < 2) {
              return 'Display name must be at least 2 characters';
            }
            return null;
          },
        ),
        
        const SizedBox(height: 16),
        
        // Email
        TextFormField(
          controller: _emailController,
          style: const TextStyle(color: AppTheme.spotifyWhite),
          keyboardType: TextInputType.emailAddress,
          decoration: InputDecoration(
            labelText: 'Email',
            labelStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppTheme.spotifyGreen, width: 2),
            ),
            filled: true,
            fillColor: AppTheme.spotifyGrey,
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Email is required';
            }
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return 'Please enter a valid email';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildPasswordSection() {
    return Column(
      children: [
        // Change Password Toggle
        ListTile(
          title: const Text(
            'Change Password',
            style: TextStyle(color: AppTheme.spotifyWhite),
          ),
          subtitle: const Text(
            'Update your account password',
            style: TextStyle(color: AppTheme.spotifyOffWhite),
          ),
          trailing: Switch(
            value: _showPasswordFields,
            onChanged: (value) {
              setState(() {
                _showPasswordFields = value;
                if (!value) {
                  _currentPasswordController.clear();
                  _newPasswordController.clear();
                  _confirmPasswordController.clear();
                }
              });
            },
            activeColor: AppTheme.spotifyGreen,
          ),
          contentPadding: EdgeInsets.zero,
        ),
        
        if (_showPasswordFields) ...[
          const SizedBox(height: 16),
          
          // Current Password
          TextFormField(
            controller: _currentPasswordController,
            obscureText: true,
            style: const TextStyle(color: AppTheme.spotifyWhite),
            decoration: InputDecoration(
              labelText: 'Current Password',
              labelStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyGreen, width: 2),
              ),
              filled: true,
              fillColor: AppTheme.spotifyGrey,
            ),
            validator: _showPasswordFields
                ? (value) {
                    if (value == null || value.isEmpty) {
                      return 'Current password is required';
                    }
                    return null;
                  }
                : null,
          ),
          
          const SizedBox(height: 16),
          
          // New Password
          TextFormField(
            controller: _newPasswordController,
            obscureText: true,
            style: const TextStyle(color: AppTheme.spotifyWhite),
            decoration: InputDecoration(
              labelText: 'New Password',
              labelStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyGreen, width: 2),
              ),
              filled: true,
              fillColor: AppTheme.spotifyGrey,
            ),
            validator: _showPasswordFields
                ? (value) {
                    if (value == null || value.isEmpty) {
                      return 'New password is required';
                    }
                    if (value.length < 6) {
                      return 'Password must be at least 6 characters';
                    }
                    return null;
                  }
                : null,
          ),
          
          const SizedBox(height: 16),
          
          // Confirm Password
          TextFormField(
            controller: _confirmPasswordController,
            obscureText: true,
            style: const TextStyle(color: AppTheme.spotifyWhite),
            decoration: InputDecoration(
              labelText: 'Confirm New Password',
              labelStyle: const TextStyle(color: AppTheme.spotifyOffWhite),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyLightGrey),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: AppTheme.spotifyGreen, width: 2),
              ),
              filled: true,
              fillColor: AppTheme.spotifyGrey,
            ),
            validator: _showPasswordFields
                ? (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please confirm your new password';
                    }
                    if (value != _newPasswordController.text) {
                      return 'Passwords do not match';
                    }
                    return null;
                  }
                : null,
          ),
        ],
      ],
    );
  }

  Widget _buildConnectedAccountsSection() {
    return Column(
      children: [
        // Spotify Connection
        _buildConnectionTile(
          title: 'Spotify',
          subtitle: 'Connect to import your Spotify playlists',
          icon: Icons.music_note,
          isConnected: false,
          onTap: _connectSpotify,
        ),
        
        const SizedBox(height: 12),
        
        // Apple Music Connection
        _buildConnectionTile(
          title: 'Apple Music',
          subtitle: 'Connect to import your Apple Music library',
          icon: Icons.apple,
          isConnected: false,
          onTap: _connectAppleMusic,
        ),
      ],
    );
  }

  Widget _buildConnectionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required bool isConnected,
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.spotifyGrey,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Icon(icon, color: AppTheme.spotifyGreen),
        title: Text(
          title,
          style: const TextStyle(
            color: AppTheme.spotifyWhite,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: const TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        trailing: ElevatedButton(
          onPressed: onTap,
          style: ElevatedButton.styleFrom(
            backgroundColor: isConnected ? AppTheme.errorRed : AppTheme.spotifyGreen,
            foregroundColor: AppTheme.spotifyBlack,
          ),
          child: Text(isConnected ? 'Disconnect' : 'Connect'),
        ),
      ),
    );
  }

  Widget _buildDangerZoneSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.errorRed.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppTheme.errorRed.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Danger Zone',
            style: TextStyle(
              color: AppTheme.errorRed,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Delete Account Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _showDeleteAccountDialog,
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: AppTheme.errorRed),
                foregroundColor: AppTheme.errorRed,
              ),
              child: const Text('Delete Account'),
            ),
          ),
        ],
      ),
    );
  }

  void _changeProfilePicture() {
    // TODO: Implement image picker
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Profile picture change coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _connectSpotify() {
    // TODO: Implement Spotify OAuth
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Spotify connection coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  void _connectAppleMusic() {
    // TODO: Implement Apple Music connection
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Apple Music connection coming soon!'),
        backgroundColor: AppTheme.spotifyGreen,
      ),
    );
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement save changes logic
      await Future.delayed(const Duration(seconds: 1)); // Simulate API call
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Account settings updated successfully!'),
            backgroundColor: AppTheme.spotifyGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update settings: $e'),
            backgroundColor: AppTheme.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.spotifyGrey,
        title: const Text(
          'Delete Account',
          style: TextStyle(color: AppTheme.errorRed),
        ),
        content: const Text(
          'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.',
          style: TextStyle(color: AppTheme.spotifyOffWhite),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppTheme.spotifyOffWhite),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement account deletion
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Account deletion coming soon!'),
                  backgroundColor: AppTheme.errorRed,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorRed,
            ),
            child: const Text('Delete Account'),
          ),
        ],
      ),
    );
  }
}
